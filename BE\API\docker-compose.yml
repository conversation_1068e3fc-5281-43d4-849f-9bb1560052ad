
services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: webtruyen_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: webtruyen_api
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - webtruyen_network

  # Redis Cache (optional)
  redis:
    image: redis:7.2-alpine
    container_name: webtruyen_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - webtruyen_network

  # Vietnamese Web Novel API
  api:
    build:
      context: ../..
      dockerfile: BE/API/Dockerfile.new
    container_name: webtruyen_api
    restart: unless-stopped
    environment:
      # Database
      MONGODB_URL: ************************************************************************
      MONGODB_DATABASE: webtruyen_api
      
      # Application
      ENVIRONMENT: production
      DEBUG: false
      HOST: 0.0.0.0
      PORT: 8000
      
      # AI Enhancement (set your actual API key)
      GOOGLE_AI_API_KEY: ${GOOGLE_AI_API_KEY}
      
      # Logging
      LOG_LEVEL: INFO
      LOG_FILE: /app/logs/api.log
      
      # Security
      ALLOWED_ORIGINS: http://localhost:3000,http://localhost:8080
      ALLOWED_HOSTS: localhost,127.0.0.1,api
      
      # Scraping
      MAX_CONCURRENT_SCRAPING: 2
      MAX_CONCURRENT_ENHANCEMENT: 1
      BROWSER_HEADLESS: true
      
      # Storage
      STORAGE_PATH: /app/storage
      EXPORT_PATH: /app/exports
      TEMP_PATH: /app/temp
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
      - ./storage:/app/storage
      - ./exports:/app/exports
      - ./temp:/app/temp
    depends_on:
      - mongodb
      - redis
    networks:
      - webtruyen_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: webtruyen_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    networks:
      - webtruyen_network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  webtruyen_network:
    driver: bridge
