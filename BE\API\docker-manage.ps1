# Vietnamese Web Novel API - Docker Management Script
# PowerShell script for easy Docker management

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("start", "stop", "restart", "logs", "status", "reset", "build", "shell", "test")]
    [string]$Action,
    
    [string]$Service = "",
    [switch]$Follow
)

Write-Host "Vietnamese Web Novel API - Docker Manager" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan

switch ($Action) {
    "start" {
        Write-Host "Starting all services..." -ForegroundColor Green
        docker-compose up -d
        Write-Host "Services started. Checking status..." -ForegroundColor Green
        Start-Sleep -Seconds 3
        docker-compose ps
    }
    
    "stop" {
        Write-Host "Stopping all services..." -ForegroundColor Yellow
        docker-compose down
        Write-Host "All services stopped." -ForegroundColor Yellow
    }
    
    "restart" {
        if ($Service) {
            Write-Host "Restarting service: $Service" -ForegroundColor Yellow
            docker-compose restart $Service
        } else {
            Write-Host "Restarting all services..." -ForegroundColor Yellow
            docker-compose restart
        }
        Write-Host "Restart completed." -ForegroundColor Green
    }
    
    "logs" {
        if ($Service) {
            Write-Host "Showing logs for service: $Service" -ForegroundColor Blue
            if ($Follow) {
                docker-compose logs -f $Service
            } else {
                docker-compose logs $Service
            }
        } else {
            Write-Host "Showing logs for all services" -ForegroundColor Blue
            if ($Follow) {
                docker-compose logs -f
            } else {
                docker-compose logs
            }
        }
    }
    
    "status" {
        Write-Host "Checking service status..." -ForegroundColor Blue
        docker-compose ps
        Write-Host "`nContainer resource usage:" -ForegroundColor Blue
        docker stats --no-stream
    }
    
    "reset" {
        Write-Host "WARNING: This will remove all containers and data!" -ForegroundColor Red
        $confirm = Read-Host "Are you sure? (y/N)"
        if ($confirm -eq "y" -or $confirm -eq "Y") {
            Write-Host "Stopping and removing all containers..." -ForegroundColor Red
            docker-compose down -v
            Write-Host "Cleaning up Docker system..." -ForegroundColor Red
            docker system prune -f
            Write-Host "Reset completed. Run 'start' to rebuild." -ForegroundColor Green
        } else {
            Write-Host "Reset cancelled." -ForegroundColor Yellow
        }
    }
    
    "build" {
        Write-Host "Building and starting services..." -ForegroundColor Green
        docker-compose up --build -d
        Write-Host "Build completed. Checking status..." -ForegroundColor Green
        Start-Sleep -Seconds 5
        docker-compose ps
    }
    
    "shell" {
        $targetService = if ($Service) { $Service } else { "api" }
        Write-Host "Opening shell in $targetService container..." -ForegroundColor Blue
        docker-compose exec $targetService bash
    }
    
    "test" {
        Write-Host "Testing API endpoints..." -ForegroundColor Blue
        
        Write-Host "`n1. Testing API health..." -ForegroundColor Yellow
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:8000/health" -Method Get
            Write-Host "✅ API Health: $($response.message)" -ForegroundColor Green
        } catch {
            Write-Host "❌ API Health check failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Write-Host "`n2. Testing database health..." -ForegroundColor Yellow
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:8000/health/database" -Method Get
            Write-Host "✅ Database Health: $($response.message)" -ForegroundColor Green
        } catch {
            Write-Host "❌ Database Health check failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Write-Host "`n3. Testing Nginx proxy..." -ForegroundColor Yellow
        try {
            $response = Invoke-RestMethod -Uri "http://localhost/health" -Method Get
            Write-Host "✅ Nginx Proxy: $($response.message)" -ForegroundColor Green
        } catch {
            Write-Host "❌ Nginx Proxy check failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Write-Host "`n4. Testing API info..." -ForegroundColor Yellow
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:8000/" -Method Get
            Write-Host "✅ API Info: $($response.name) v$($response.version)" -ForegroundColor Green
        } catch {
            Write-Host "❌ API Info check failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Write-Host "`n5. Checking container status..." -ForegroundColor Yellow
        docker-compose ps
    }
}

Write-Host "`nDocker management completed." -ForegroundColor Cyan
Write-Host "Available commands:" -ForegroundColor Gray
Write-Host "  start   - Start all services" -ForegroundColor Gray
Write-Host "  stop    - Stop all services" -ForegroundColor Gray
Write-Host "  restart - Restart services" -ForegroundColor Gray
Write-Host "  logs    - View logs (use -Service and -Follow)" -ForegroundColor Gray
Write-Host "  status  - Check service status" -ForegroundColor Gray
Write-Host "  reset   - Reset everything (removes data)" -ForegroundColor Gray
Write-Host "  build   - Build and start services" -ForegroundColor Gray
Write-Host "  shell   - Open container shell" -ForegroundColor Gray
Write-Host "  test    - Test all endpoints" -ForegroundColor Gray
